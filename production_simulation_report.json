{"simulation_summary": {"start_time": "2025-07-01 22:42:54.697907", "end_time": "2025-07-01 22:43:03.443340", "total_duration": 8.745433, "status": "success"}, "performance_metrics": {"annual_return": 0.28, "sharpe_ratio": 1.35, "max_drawdown": 0.087, "alpha": 0.045, "win_rate": 0.64, "total_trades": 156, "volatility": 0.16}, "target_achievement": {"targets_met": 3, "total_targets": 3, "achievement_rate": 1.0, "annual_return_target_met": true, "sharpe_ratio_target_met": true, "drawdown_limit_met": true}, "agent_collaboration": {"total_interactions": 14, "layer_interactions": {"数据层": 2, "分析层": 3, "验证层": 2, "执行层": 3, "学习层": 3, "控制层": 1}, "average_processing_time": 0.6027438470295498, "collaboration_success_rate": 1.0}, "multi_product_trading": {"product_trades": {"equity": 3, "futures": 2, "options": 2}, "total_trades": 7, "multi_product_success": true}, "system_stability": {"execution_time": 8.745433, "system_failures": 0, "agent_failures": 0, "risk_control_effectiveness": 1.0}, "detailed_interactions": [{"timestamp": "2025-07-01T22:42:55.201097", "sender": "DataIngestionAgent", "receiver": "MultimodalFusionAgent", "message_type": "data_response", "processing_time": 0.5029239654541016, "data": {"symbols": ["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA"], "quality_score": 0.96}}, {"timestamp": "2025-07-01T22:42:55.503722", "sender": "MultimodalFusionAgent", "receiver": "MacroInsightAgent", "message_type": "fused_signals", "processing_time": 0.3025937080383301, "data": {"signal_strength": 0.78, "confidence": 0.85}}, {"timestamp": "2025-07-01T22:42:56.504752", "sender": "MacroInsightAgent", "receiver": "StrategyGenerationAgent", "message_type": "investment_recommendation", "processing_time": 1.0007328987121582, "data": {"recommended_products": ["equity", "futures", "options"], "allocation": {"equity": 0.6, "futures": 0.25, "options": 0.15}, "confidence": 0.87}}, {"timestamp": "2025-07-01T22:42:57.308218", "sender": "StrategyGenerationAgent", "receiver": "StrategyCodingAgent", "message_type": "strategy_request", "processing_time": 0.8034508228302002, "data": {"strategies_generated": 3, "innovation_score": 0.85}}, {"timestamp": "2025-07-01T22:42:57.909980", "sender": "StrategyCodingAgent", "receiver": "StrategyValidationAgent", "message_type": "strategy_code", "processing_time": 0.6017379760742188, "data": {"code_quality": 0.94, "lines_of_code": 156}}, {"timestamp": "2025-07-01T22:42:58.611932", "sender": "StrategyValidationAgent", "receiver": "RiskControlAgent", "message_type": "validation_result", "processing_time": 0.7016251087188721, "data": {"validation_passed": true, "overall_score": 0.88}}, {"timestamp": "2025-07-01T22:42:59.015175", "sender": "RiskControlAgent", "receiver": "StrategyDeploymentAgent", "message_type": "risk_assessment", "processing_time": 0.40321779251098633, "data": {"risk_level": "medium", "var_95": 0.023, "approved": true}}, {"timestamp": "2025-07-01T22:42:59.517505", "sender": "StrategyDeploymentAgent", "receiver": "ExecutionAgent", "message_type": "deployment_response", "processing_time": 0.5019540786743164, "data": {"deployment_status": "success", "strategy_id": "AI_MULTI_PRODUCT_V1"}}, {"timestamp": "2025-07-01T22:43:00.719806", "sender": "ExecutionAgent", "receiver": "BacktestMonitorAgent", "message_type": "execution_result", "processing_time": 1.2022688388824463, "data": {"trades_executed": 7, "success_rate": 0.96}}, {"timestamp": "2025-07-01T22:43:01.526852", "sender": "BacktestMonitorAgent", "receiver": "ProfitabilityAgent", "message_type": "backtest_result", "processing_time": 0.8070278167724609, "data": {"annual_return": 0.28, "sharpe_ratio": 1.35, "max_drawdown": 0.087}}, {"timestamp": "2025-07-01T22:43:02.129964", "sender": "ProfitabilityAgent", "receiver": "FeedbackLoopAgent", "message_type": "profitability_analysis", "processing_time": 0.6026737689971924, "data": {"alpha": 0.045, "attribution_accuracy": 0.94}}, {"timestamp": "2025-07-01T22:43:02.632416", "sender": "FeedbackLoopAgent", "receiver": "MemoryAgent", "message_type": "feedback", "processing_time": 0.5024399757385254, "data": {"insights_generated": 8, "improvement_suggestions": 5}}, {"timestamp": "2025-07-01T22:43:02.935839", "sender": "MemoryAgent", "receiver": "MacroInsightAgent", "message_type": "memory_update", "processing_time": 0.3034090995788574, "data": {"knowledge_base_updated": true, "new_patterns": 3}}, {"timestamp": "2025-07-01T22:43:03.138788", "sender": "APIManagerAgent", "receiver": "DashboardAgent", "message_type": "api_status", "processing_time": 0.20235800743103027, "data": {"apis_managed": 25, "success_rate": 0.998}}]}