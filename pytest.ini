[tool:pytest]
# Pytest configuration for QuantAI AutoGen testing

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Output and reporting
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --disable-warnings
    --cov=quantai
    --cov-report=html:test_results/coverage_html
    --cov-report=xml:test_results/coverage.xml
    --cov-report=json:test_results/coverage.json
    --cov-report=term-missing
    --cov-fail-under=75
    --junit-xml=test_results/junit.xml
    --html=test_results/pytest_report.html
    --self-contained-html

# Markers for test categorization
markers =
    unit: Unit tests
    integration: Integration tests
    performance: Performance and benchmark tests
    slow: Slow running tests
    fast: Fast running tests
    critical: Critical path tests
    smoke: Smoke tests for basic functionality
    regression: Regression tests
    security: Security-related tests
    data: Data processing tests
    strategy: Strategy-related tests
    risk: Risk management tests
    execution: Trade execution tests
    control: Control plane tests
    emergency: Emergency scenario tests

# Test timeouts
timeout = 300
timeout_method = thread

# Asyncio configuration
asyncio_mode = auto

# Logging configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Warnings configuration
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:asyncio
    error::UserWarning:quantai

# Minimum Python version
minversion = 3.9

# Test collection configuration
collect_ignore = [
    "setup.py",
    "docs/",
    "build/",
    "dist/",
    ".tox/",
    ".venv/",
    "venv/",
    "env/"
]

# Parallel execution
# Uncomment to enable parallel test execution
# addopts = -n auto
