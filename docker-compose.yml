version: '3.8'

services:
  # Main QuantAI Application
  quantai-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: quantai-app
    environment:
      - QUANTAI_ENV=development
      - DATABASE_URL=******************************************/quantai
      - REDIS_URL=redis://redis:6379/0
      - CHROMA_PERSIST_DIRECTORY=/app/data/memory
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    ports:
      - "8000:8000"  # API server
    depends_on:
      - postgres
      - redis
      - chroma
    restart: unless-stopped
    networks:
      - quantai-network

  # Dashboard
  quantai-dashboard:
    build:
      context: .
      dockerfile: Dockerfile.dashboard
    container_name: quantai-dashboard
    environment:
      - QUANTAI_API_URL=http://quantai-app:8000
    ports:
      - "8501:8501"  # Streamlit dashboard
    depends_on:
      - quantai-app
    restart: unless-stopped
    networks:
      - quantai-network

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: quantai-postgres
    environment:
      - POSTGRES_DB=quantai
      - POSTGRES_USER=quantai
      - POSTGRES_PASSWORD=quantai
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - quantai-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: quantai-redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - quantai-network

  # ChromaDB Vector Database
  chroma:
    image: chromadb/chroma:latest
    container_name: quantai-chroma
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
    volumes:
      - chroma_data:/chroma/chroma
    ports:
      - "8001:8000"  # ChromaDB API
    restart: unless-stopped
    networks:
      - quantai-network

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: quantai-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    restart: unless-stopped
    networks:
      - quantai-network

  # Grafana Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: quantai-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - quantai-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: quantai-nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - quantai-app
      - quantai-dashboard
      - grafana
    restart: unless-stopped
    networks:
      - quantai-network

volumes:
  postgres_data:
  redis_data:
  chroma_data:
  prometheus_data:
  grafana_data:

networks:
  quantai-network:
    driver: bridge
