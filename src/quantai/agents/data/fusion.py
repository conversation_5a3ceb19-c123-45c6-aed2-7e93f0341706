"""
Multimodal Fusion Agent (D4) for the QuantAI system.

This agent fuses structured data, text, and image signals to create
comprehensive market insights using advanced multimodal AI techniques.
"""

import asyncio
import json
import numpy as np
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from autogen_core import MessageContext
from autogen_core.models import ChatCompletionClient, UserMessage
from loguru import logger
import pandas as pd

from ...core.base import AgentCapability, AgentRole, ModelCapableAgent
from ...core.messages import DataMessage, MessageType, QuantMessage


class MultimodalFusionAgent(ModelCapableAgent):
    """
    Multimodal Fusion Agent (D4) - Fuses structured data, text, and image signals.
    
    Capabilities:
    - Structured data integration (market data, financials, economic indicators)
    - Text signal processing (news, earnings calls, social media)
    - Image signal analysis (charts, technical patterns, satellite imagery)
    - Cross-modal correlation analysis
    - Unified signal generation for downstream agents
    """
    
    def __init__(
        self,
        model_client: ChatCompletionClient,
        fusion_algorithms: List[str] = None,
        confidence_threshold: float = 0.7,
        **kwargs
    ):
        super().__init__(
            role=AgentRole.MULTIMODAL_FUSION,
            capabilities=[
                AgentCapability.DATA_PROCESSING,
                AgentCapability.MULTIMODAL_ANALYSIS,
            ],
            model_client=model_client,
            system_message=self._get_system_message(),
            **kwargs
        )
        
        self.fusion_algorithms = fusion_algorithms or ["weighted_average", "attention", "ensemble"]
        self.confidence_threshold = confidence_threshold
        self._signal_weights = self._initialize_signal_weights()
        self._fusion_history: List[Dict[str, Any]] = []
    
    def _get_system_message(self) -> str:
        return """You are a Multimodal Fusion Agent specializing in integrating diverse financial data signals.

Your responsibilities:
1. Fuse structured market data (prices, volumes, ratios) with unstructured text and image data
2. Analyze correlations between different data modalities
3. Generate unified, weighted signals for trading strategy development
4. Assess signal confidence and reliability across modalities
5. Detect conflicting signals and resolve inconsistencies
6. Provide comprehensive market insights combining all available data

Fusion Techniques:
- Weighted averaging based on signal reliability
- Attention mechanisms for dynamic weighting
- Ensemble methods for robust signal generation
- Cross-modal correlation analysis
- Temporal alignment of multi-frequency data

Guidelines:
- Prioritize signal quality and consistency over quantity
- Always provide confidence scores for fused signals
- Identify and flag conflicting or anomalous signals
- Maintain transparency in fusion methodology
- Adapt weights based on historical performance
- Consider market regime changes in fusion parameters

Focus on creating robust, reliable signals that capture the full spectrum of available market information."""
    
    def _initialize_signal_weights(self) -> Dict[str, float]:
        """Initialize default weights for different signal types."""
        return {
            "market_data": 0.4,      # Price, volume, technical indicators
            "news_sentiment": 0.25,   # News sentiment and analysis
            "earnings_data": 0.15,    # Fundamental data
            "social_sentiment": 0.1,  # Social media sentiment
            "macro_indicators": 0.1,  # Economic indicators
        }
    
    async def process_message(
        self, 
        message: QuantMessage, 
        ctx: MessageContext
    ) -> Optional[QuantMessage]:
        """Process incoming data for multimodal fusion."""
        
        if not isinstance(message, DataMessage):
            logger.warning(f"Received non-data message: {type(message)}")
            return None
        
        if message.message_type != MessageType.DATA_RESPONSE:
            return None
        
        logger.info(f"Processing {message.data_type} data for fusion")
        
        try:
            # Store incoming data for fusion
            await self._store_signal_data(message)
            
            # Check if we have enough data for fusion
            if await self._ready_for_fusion(message.symbols):
                # Perform multimodal fusion
                fused_signals = await self._perform_fusion(message.symbols)
                
                # Create fused data response
                response = DataMessage(
                    message_type=MessageType.DATA_RESPONSE,
                    sender_id=self.agent_id,
                    data_type="fused_signals",
                    symbols=message.symbols,
                    data_payload=fused_signals,
                    source=self.agent_id,
                    quality_score=self._assess_fusion_quality(fused_signals),
                    session_id=message.session_id,
                    correlation_id=message.correlation_id,
                )
                
                logger.info("Successfully generated fused signals")
                return response
            
            return None
            
        except Exception as e:
            logger.error(f"Error in multimodal fusion: {e}")
            return None
    
    async def _store_signal_data(self, message: DataMessage):
        """Store incoming signal data for fusion processing."""
        timestamp = datetime.now()
        
        signal_entry = {
            "timestamp": timestamp,
            "data_type": message.data_type,
            "symbols": message.symbols,
            "data": message.data_payload,
            "quality_score": message.quality_score,
            "source": message.source,
        }
        
        # Store in fusion history (keep last 100 entries)
        self._fusion_history.append(signal_entry)
        if len(self._fusion_history) > 100:
            self._fusion_history.pop(0)
        
        logger.debug(f"Stored {message.data_type} signal data")
    
    async def _ready_for_fusion(self, symbols: List[str]) -> bool:
        """Check if we have sufficient data for fusion."""
        if not symbols:
            return False
        
        # Get recent data (last 1 hour)
        recent_cutoff = datetime.now().timestamp() - 3600
        recent_data = [
            entry for entry in self._fusion_history
            if entry["timestamp"].timestamp() > recent_cutoff
        ]
        
        # Check if we have data from multiple modalities
        data_types = set(entry["data_type"] for entry in recent_data)
        required_types = {"market", "news"}  # Minimum required
        
        return len(data_types.intersection(required_types)) >= len(required_types)
    
    async def _perform_fusion(self, symbols: List[str]) -> Dict[str, Any]:
        """Perform multimodal fusion to generate unified signals."""
        logger.info("Performing multimodal fusion")
        
        # Collect recent data by type
        recent_data = self._get_recent_data_by_type()
        
        fused_signals = {}
        
        for symbol in symbols:
            try:
                # Extract signals for this symbol
                symbol_signals = await self._extract_symbol_signals(symbol, recent_data)
                
                # Apply fusion algorithms
                fused_signal = await self._apply_fusion_algorithms(symbol, symbol_signals)
                
                fused_signals[symbol] = fused_signal
                
            except Exception as e:
                logger.error(f"Error fusing signals for {symbol}: {e}")
                fused_signals[symbol] = {"error": str(e)}
        
        # Add metadata
        fused_signals["fusion_metadata"] = {
            "timestamp": datetime.now().isoformat(),
            "algorithms_used": self.fusion_algorithms,
            "signal_weights": self._signal_weights,
            "data_sources": list(recent_data.keys()),
        }
        
        return fused_signals
    
    def _get_recent_data_by_type(self) -> Dict[str, List[Dict[str, Any]]]:
        """Organize recent data by type."""
        recent_cutoff = datetime.now().timestamp() - 3600  # Last hour
        
        data_by_type = {}
        for entry in self._fusion_history:
            if entry["timestamp"].timestamp() > recent_cutoff:
                data_type = entry["data_type"]
                if data_type not in data_by_type:
                    data_by_type[data_type] = []
                data_by_type[data_type].append(entry)
        
        return data_by_type
    
    async def _extract_symbol_signals(
        self, 
        symbol: str, 
        data_by_type: Dict[str, List[Dict[str, Any]]]
    ) -> Dict[str, Any]:
        """Extract signals for a specific symbol from all data types."""
        signals = {}
        
        # Market data signals
        if "market" in data_by_type:
            market_signals = await self._extract_market_signals(symbol, data_by_type["market"])
            signals["market"] = market_signals
        
        # News sentiment signals
        if "news" in data_by_type:
            news_signals = await self._extract_news_signals(symbol, data_by_type["news"])
            signals["news"] = news_signals
        
        # Earnings signals
        if "earnings" in data_by_type:
            earnings_signals = await self._extract_earnings_signals(symbol, data_by_type["earnings"])
            signals["earnings"] = earnings_signals
        
        # Sentiment signals
        if "sentiment" in data_by_type:
            sentiment_signals = await self._extract_sentiment_signals(symbol, data_by_type["sentiment"])
            signals["sentiment"] = sentiment_signals
        
        return signals
    
    async def _extract_market_signals(
        self, 
        symbol: str, 
        market_data: List[Dict[str, Any]]
    ) -> Dict[str, float]:
        """Extract trading signals from market data."""
        signals = {}
        
        for data_entry in market_data:
            symbol_data = data_entry["data"].get(symbol, {})
            
            if "technical_indicators" in symbol_data:
                indicators = symbol_data["technical_indicators"]
                
                # RSI signal
                rsi = indicators.get("rsi", 50)
                if rsi < 30:
                    signals["rsi_signal"] = 1.0  # Oversold - buy signal
                elif rsi > 70:
                    signals["rsi_signal"] = -1.0  # Overbought - sell signal
                else:
                    signals["rsi_signal"] = 0.0  # Neutral
                
                # Moving average signal
                sma_20 = indicators.get("sma_20", 0)
                sma_50 = indicators.get("sma_50", 0)
                if sma_20 > sma_50:
                    signals["ma_signal"] = 1.0  # Bullish
                elif sma_20 < sma_50:
                    signals["ma_signal"] = -1.0  # Bearish
                else:
                    signals["ma_signal"] = 0.0
                
                # Volume signal
                volume_ratio = indicators.get("volume_ratio", 1.0)
                if volume_ratio > 1.5:
                    signals["volume_signal"] = 1.0  # High volume
                elif volume_ratio < 0.5:
                    signals["volume_signal"] = -1.0  # Low volume
                else:
                    signals["volume_signal"] = 0.0
        
        return signals
    
    async def _extract_news_signals(
        self, 
        symbol: str, 
        news_data: List[Dict[str, Any]]
    ) -> Dict[str, float]:
        """Extract signals from news sentiment analysis."""
        signals = {"sentiment_score": 0.0, "news_volume": 0.0}
        
        sentiment_scores = []
        article_count = 0
        
        for data_entry in news_data:
            articles = data_entry["data"].get("processed_articles", [])
            
            for article in articles:
                analysis = article.get("analysis", {})
                
                # Check if article mentions the symbol
                symbols_mentioned = analysis.get("symbols_mentioned", [])
                if symbol in symbols_mentioned:
                    sentiment = analysis.get("sentiment_score", 0.0)
                    sentiment_scores.append(sentiment)
                    article_count += 1
        
        if sentiment_scores:
            signals["sentiment_score"] = np.mean(sentiment_scores)
            signals["news_volume"] = min(article_count / 10.0, 1.0)  # Normalize to 0-1
        
        return signals
    
    async def _extract_earnings_signals(
        self, 
        symbol: str, 
        earnings_data: List[Dict[str, Any]]
    ) -> Dict[str, float]:
        """Extract signals from earnings data."""
        signals = {"earnings_surprise": 0.0, "growth_trend": 0.0}
        
        # This would analyze earnings surprises, growth trends, etc.
        # For now, return neutral signals
        return signals
    
    async def _extract_sentiment_signals(
        self, 
        symbol: str, 
        sentiment_data: List[Dict[str, Any]]
    ) -> Dict[str, float]:
        """Extract signals from social sentiment data."""
        signals = {"social_sentiment": 0.0}
        
        for data_entry in sentiment_data:
            symbol_sentiment = data_entry["data"].get("source_sentiments", {}).get(symbol, {})
            if symbol_sentiment:
                signals["social_sentiment"] = symbol_sentiment.get("overall", 0.0)
        
        return signals
    
    async def _apply_fusion_algorithms(
        self, 
        symbol: str, 
        signals: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Apply fusion algorithms to combine signals."""
        
        # Weighted average fusion
        weighted_signal = await self._weighted_average_fusion(signals)
        
        # Attention-based fusion
        attention_signal = await self._attention_fusion(signals)
        
        # Ensemble fusion
        ensemble_signal = await self._ensemble_fusion(signals)
        
        # Generate confidence score
        confidence = await self._calculate_confidence(signals)
        
        # Use LLM for final signal interpretation
        llm_analysis = await self._llm_signal_analysis(symbol, signals)
        
        return {
            "weighted_signal": weighted_signal,
            "attention_signal": attention_signal,
            "ensemble_signal": ensemble_signal,
            "final_signal": ensemble_signal,  # Use ensemble as final
            "confidence": confidence,
            "llm_analysis": llm_analysis,
            "component_signals": signals,
        }
    
    async def _weighted_average_fusion(self, signals: Dict[str, Any]) -> float:
        """Perform weighted average fusion of signals."""
        total_signal = 0.0
        total_weight = 0.0
        
        for signal_type, signal_data in signals.items():
            if isinstance(signal_data, dict):
                weight = self._signal_weights.get(signal_type, 0.1)
                
                # Average the signals within this type
                signal_values = [v for v in signal_data.values() if isinstance(v, (int, float))]
                if signal_values:
                    avg_signal = np.mean(signal_values)
                    total_signal += weight * avg_signal
                    total_weight += weight
        
        return total_signal / total_weight if total_weight > 0 else 0.0
    
    async def _attention_fusion(self, signals: Dict[str, Any]) -> float:
        """Perform attention-based fusion of signals."""
        # Simplified attention mechanism
        # In practice, this would use learned attention weights
        
        signal_values = []
        signal_weights = []
        
        for signal_type, signal_data in signals.items():
            if isinstance(signal_data, dict):
                # Calculate attention weight based on signal variance and quality
                signal_vals = [v for v in signal_data.values() if isinstance(v, (int, float))]
                if signal_vals:
                    variance = np.var(signal_vals)
                    attention_weight = 1.0 / (1.0 + variance)  # Lower variance = higher attention
                    
                    signal_values.append(np.mean(signal_vals))
                    signal_weights.append(attention_weight)
        
        if signal_values:
            # Normalize weights
            signal_weights = np.array(signal_weights)
            signal_weights = signal_weights / np.sum(signal_weights)
            
            return np.dot(signal_values, signal_weights)
        
        return 0.0
    
    async def _ensemble_fusion(self, signals: Dict[str, Any]) -> float:
        """Perform ensemble fusion of signals."""
        # Combine weighted average and attention fusion
        weighted = await self._weighted_average_fusion(signals)
        attention = await self._attention_fusion(signals)
        
        # Simple ensemble average
        return (weighted + attention) / 2.0
    
    async def _calculate_confidence(self, signals: Dict[str, Any]) -> float:
        """Calculate confidence score for the fused signal."""
        signal_values = []
        
        for signal_data in signals.values():
            if isinstance(signal_data, dict):
                vals = [v for v in signal_data.values() if isinstance(v, (int, float))]
                signal_values.extend(vals)
        
        if not signal_values:
            return 0.0
        
        # Confidence based on signal consistency
        signal_std = np.std(signal_values)
        confidence = 1.0 / (1.0 + signal_std)  # Lower std = higher confidence
        
        return min(confidence, 1.0)
    
    async def _llm_signal_analysis(self, symbol: str, signals: Dict[str, Any]) -> Dict[str, Any]:
        """Use LLM to analyze and interpret the fused signals."""
        
        prompt = f"""Analyze the following multimodal signals for {symbol}:

{json.dumps(signals, indent=2)}

Please provide:
1. Overall market sentiment interpretation
2. Key signal conflicts or confirmations
3. Recommended trading action (buy/sell/hold)
4. Risk assessment
5. Confidence level in the analysis

Respond in JSON format:
{{
    "sentiment": "bullish/bearish/neutral",
    "action": "buy/sell/hold",
    "confidence": 0.0-1.0,
    "risk_level": "low/medium/high",
    "key_insights": ["insight1", "insight2"],
    "signal_conflicts": ["conflict1", "conflict2"]
}}"""
        
        try:
            response = await self._call_model([UserMessage(content=prompt, source="user")])
            return json.loads(response)
        except Exception as e:
            logger.error(f"Error in LLM signal analysis: {e}")
            return {"error": str(e)}
    
    def _assess_fusion_quality(self, fused_signals: Dict[str, Any]) -> float:
        """Assess the quality of the fused signals."""
        if not fused_signals:
            return 0.0
        
        quality_score = 1.0
        
        # Check for errors
        error_count = sum(
            1 for signal in fused_signals.values() 
            if isinstance(signal, dict) and "error" in signal
        )
        
        if error_count > 0:
            quality_score -= (error_count / len(fused_signals)) * 0.5
        
        # Check confidence scores
        confidence_scores = []
        for signal in fused_signals.values():
            if isinstance(signal, dict) and "confidence" in signal:
                confidence_scores.append(signal["confidence"])
        
        if confidence_scores:
            avg_confidence = np.mean(confidence_scores)
            quality_score *= avg_confidence
        
        return max(0.0, min(1.0, quality_score))
