"""
Data Ingestion Agent (D1) for the QuantAI system.

This agent is responsible for crawling and cleaning market data, news,
earnings reports, and sentiment data from various sources using web scraping
and API integrations.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import aiohttp
import pandas as pd
import yfinance as yf
from autogen_core import MessageContext
from autogen_core.models import ChatCompletionClient, UserMessage
from bs4 import BeautifulSoup
from loguru import logger
from playwright.async_api import async_playwright

from ...core.base import AgentCapability, AgentRole, ModelCapableAgent
from ...core.messages import DataMessage, MessageType, QuantMessage


class DataIngestionAgent(ModelCapableAgent):
    """
    Data Ingestion Agent (D1) - Crawls and cleans financial data.
    
    Capabilities:
    - Market data collection (prices, volumes, technical indicators)
    - News article scraping and processing
    - Earnings report extraction
    - Sentiment data aggregation
    - Data quality assessment and cleaning
    """
    
    def __init__(
        self,
        model_client: ChatCompletionClient,
        crawl_interval_minutes: int = 15,
        max_concurrent_crawls: int = 10,
        **kwargs
    ):
        super().__init__(
            role=AgentRole.DATA_INGESTION,
            capabilities=[
                AgentCapability.DATA_COLLECTION,
                AgentCapability.DATA_PROCESSING,
            ],
            model_client=model_client,
            system_message=self._get_system_message(),
            **kwargs
        )
        
        self.crawl_interval_minutes = crawl_interval_minutes
        self.max_concurrent_crawls = max_concurrent_crawls
        self._data_sources = self._initialize_data_sources()
        self._crawl_semaphore = asyncio.Semaphore(max_concurrent_crawls)
        self._last_crawl_times: Dict[str, datetime] = {}
    
    def _get_system_message(self) -> str:
        return """You are a Data Ingestion Agent specializing in financial data collection and processing.

Your responsibilities:
1. Collect market data (prices, volumes, technical indicators) from various sources
2. Scrape and process financial news articles
3. Extract earnings report data and key metrics
4. Aggregate sentiment data from social media and news
5. Assess data quality and perform cleaning operations
6. Provide structured, clean data to other agents

Guidelines:
- Prioritize data quality and accuracy over speed
- Always include data quality scores and metadata
- Handle rate limits and API restrictions gracefully
- Detect and flag anomalous or suspicious data
- Maintain data lineage and source attribution
- Use appropriate error handling for network operations

Focus on providing high-quality, timely financial data that other agents can rely on for analysis and decision-making."""
    
    def _initialize_data_sources(self) -> Dict[str, Dict[str, Any]]:
        """Initialize configuration for various data sources."""
        return {
            "market_data": {
                "yfinance": {"enabled": True, "rate_limit": 2000},  # requests per hour
                "alpha_vantage": {"enabled": False, "rate_limit": 500},
                "polygon": {"enabled": False, "rate_limit": 1000},
            },
            "news": {
                "newsapi": {"enabled": True, "rate_limit": 1000},
                "finnhub": {"enabled": False, "rate_limit": 300},
                "reuters": {"enabled": True, "rate_limit": None},  # Web scraping
                "bloomberg": {"enabled": True, "rate_limit": None},
            },
            "earnings": {
                "sec_edgar": {"enabled": True, "rate_limit": 10},
                "earnings_call_transcripts": {"enabled": True, "rate_limit": None},
            },
            "sentiment": {
                "twitter": {"enabled": False, "rate_limit": 300},
                "reddit": {"enabled": True, "rate_limit": 60},
                "stocktwits": {"enabled": True, "rate_limit": 200},
            }
        }
    
    async def process_message(
        self, 
        message: QuantMessage, 
        ctx: MessageContext
    ) -> Optional[QuantMessage]:
        """Process incoming data requests."""
        
        if not isinstance(message, DataMessage):
            logger.warning(f"Received non-data message: {type(message)}")
            return None
        
        if message.message_type != MessageType.DATA_REQUEST:
            return None
        
        logger.info(f"Processing data request for {message.data_type}")
        
        try:
            # Route to appropriate data collection method
            if message.data_type == "market":
                data = await self._collect_market_data(message)
            elif message.data_type == "news":
                data = await self._collect_news_data(message)
            elif message.data_type == "earnings":
                data = await self._collect_earnings_data(message)
            elif message.data_type == "sentiment":
                data = await self._collect_sentiment_data(message)
            else:
                logger.error(f"Unknown data type: {message.data_type}")
                return None
            
            # Create response message
            response = DataMessage(
                message_type=MessageType.DATA_RESPONSE,
                sender_id=self.agent_id,
                recipient_id=message.sender_id,
                data_type=message.data_type,
                symbols=message.symbols,
                start_date=message.start_date,
                end_date=message.end_date,
                data_payload=data,
                source=self.agent_id,
                quality_score=self._assess_data_quality(data),
                session_id=message.session_id,
                correlation_id=message.correlation_id,
            )
            
            logger.info(f"Successfully collected {message.data_type} data")
            return response
            
        except Exception as e:
            logger.error(f"Error collecting {message.data_type} data: {e}")
            return None
    
    async def _collect_market_data(self, message: DataMessage) -> Dict[str, Any]:
        """Collect market data for specified symbols."""
        symbols = message.symbols or ["SPY", "QQQ", "IWM"]
        start_date = message.start_date or (datetime.now() - timedelta(days=30))
        end_date = message.end_date or datetime.now()
        
        market_data = {}
        
        async with self._crawl_semaphore:
            for symbol in symbols:
                try:
                    # Use yfinance for market data
                    ticker = yf.Ticker(symbol)
                    
                    # Get historical data
                    hist_data = ticker.history(
                        start=start_date.date(),
                        end=end_date.date(),
                        interval="1d"
                    )
                    
                    # Get additional info
                    info = ticker.info
                    
                    # Get technical indicators
                    technical_indicators = await self._calculate_technical_indicators(hist_data)
                    
                    market_data[symbol] = {
                        "historical_data": hist_data.to_dict("records"),
                        "info": info,
                        "technical_indicators": technical_indicators,
                        "last_updated": datetime.now().isoformat(),
                    }
                    
                    logger.debug(f"Collected market data for {symbol}")
                    
                except Exception as e:
                    logger.error(f"Error collecting market data for {symbol}: {e}")
                    market_data[symbol] = {"error": str(e)}
        
        return market_data
    
    async def _calculate_technical_indicators(self, data: pd.DataFrame) -> Dict[str, float]:
        """Calculate technical indicators for market data."""
        if data.empty:
            return {}
        
        indicators = {}
        
        try:
            # Simple moving averages
            indicators["sma_20"] = data["Close"].rolling(20).mean().iloc[-1]
            indicators["sma_50"] = data["Close"].rolling(50).mean().iloc[-1]
            indicators["sma_200"] = data["Close"].rolling(200).mean().iloc[-1]
            
            # RSI
            delta = data["Close"].diff()
            gain = (delta.where(delta > 0, 0)).rolling(14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
            rs = gain / loss
            indicators["rsi"] = 100 - (100 / (1 + rs.iloc[-1]))
            
            # Bollinger Bands
            sma_20 = data["Close"].rolling(20).mean()
            std_20 = data["Close"].rolling(20).std()
            indicators["bb_upper"] = (sma_20 + 2 * std_20).iloc[-1]
            indicators["bb_lower"] = (sma_20 - 2 * std_20).iloc[-1]
            
            # Volume indicators
            indicators["avg_volume_20"] = data["Volume"].rolling(20).mean().iloc[-1]
            indicators["volume_ratio"] = data["Volume"].iloc[-1] / indicators["avg_volume_20"]
            
        except Exception as e:
            logger.error(f"Error calculating technical indicators: {e}")
        
        return indicators
    
    async def _collect_news_data(self, message: DataMessage) -> Dict[str, Any]:
        """Collect news data related to specified symbols or topics."""
        symbols = message.symbols or []
        
        news_data = {
            "articles": [],
            "sources": [],
            "sentiment_summary": {},
        }
        
        # Collect from multiple sources
        async with self._crawl_semaphore:
            # Web scraping for major financial news sites
            scraped_articles = await self._scrape_financial_news(symbols)
            news_data["articles"].extend(scraped_articles)
            
            # Process articles with LLM for sentiment and key insights
            processed_articles = await self._process_news_articles(news_data["articles"])
            news_data["processed_articles"] = processed_articles
        
        return news_data
    
    async def _scrape_financial_news(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """Scrape financial news from major websites."""
        articles = []
        
        try:
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page()
                
                # Reuters Finance
                await page.goto("https://www.reuters.com/business/finance/")
                await page.wait_for_load_state("networkidle")
                
                # Extract article links and titles
                article_elements = await page.query_selector_all("article")
                
                for element in article_elements[:10]:  # Limit to 10 articles
                    try:
                        title_element = await element.query_selector("h3, h2")
                        link_element = await element.query_selector("a")
                        
                        if title_element and link_element:
                            title = await title_element.inner_text()
                            link = await link_element.get_attribute("href")
                            
                            if link and not link.startswith("http"):
                                link = f"https://www.reuters.com{link}"
                            
                            articles.append({
                                "title": title,
                                "url": link,
                                "source": "Reuters",
                                "scraped_at": datetime.now().isoformat(),
                            })
                    except Exception as e:
                        logger.debug(f"Error extracting article: {e}")
                
                await browser.close()
                
        except Exception as e:
            logger.error(f"Error scraping financial news: {e}")
        
        return articles
    
    async def _process_news_articles(self, articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process news articles using LLM for sentiment and insights."""
        processed = []
        
        for article in articles[:5]:  # Process first 5 articles
            try:
                prompt = f"""Analyze this financial news article:

Title: {article.get('title', '')}
URL: {article.get('url', '')}

Please provide:
1. Sentiment score (-1 to 1, where -1 is very negative, 0 is neutral, 1 is very positive)
2. Key insights (3-5 bullet points)
3. Relevant symbols/companies mentioned
4. Market impact assessment (low/medium/high)

Respond in JSON format:
{{
    "sentiment_score": 0.0,
    "key_insights": ["insight1", "insight2"],
    "symbols_mentioned": ["AAPL", "MSFT"],
    "market_impact": "medium"
}}"""
                
                response = await self._call_model([UserMessage(content=prompt, source="user")])
                
                try:
                    analysis = json.loads(response)
                    article["analysis"] = analysis
                except json.JSONDecodeError:
                    logger.warning("Failed to parse LLM response as JSON")
                    article["analysis"] = {"error": "Failed to parse analysis"}
                
                processed.append(article)
                
            except Exception as e:
                logger.error(f"Error processing article: {e}")
                article["analysis"] = {"error": str(e)}
                processed.append(article)
        
        return processed
    
    async def _collect_earnings_data(self, message: DataMessage) -> Dict[str, Any]:
        """Collect earnings data for specified symbols."""
        symbols = message.symbols or []
        
        earnings_data = {}
        
        for symbol in symbols:
            try:
                ticker = yf.Ticker(symbol)
                
                # Get earnings data
                earnings = ticker.earnings
                quarterly_earnings = ticker.quarterly_earnings
                
                earnings_data[symbol] = {
                    "annual_earnings": earnings.to_dict() if not earnings.empty else {},
                    "quarterly_earnings": quarterly_earnings.to_dict() if not quarterly_earnings.empty else {},
                    "last_updated": datetime.now().isoformat(),
                }
                
            except Exception as e:
                logger.error(f"Error collecting earnings data for {symbol}: {e}")
                earnings_data[symbol] = {"error": str(e)}
        
        return earnings_data
    
    async def _collect_sentiment_data(self, message: DataMessage) -> Dict[str, Any]:
        """Collect sentiment data from various sources."""
        symbols = message.symbols or []
        
        sentiment_data = {
            "overall_sentiment": 0.0,
            "source_sentiments": {},
            "sentiment_trends": {},
        }
        
        # This would integrate with social media APIs and sentiment analysis
        # For now, we'll simulate sentiment data
        for symbol in symbols:
            sentiment_data["source_sentiments"][symbol] = {
                "reddit": 0.2,  # Slightly positive
                "twitter": -0.1,  # Slightly negative
                "news": 0.3,  # Positive
                "overall": 0.13,  # Average
            }
        
        return sentiment_data
    
    def _assess_data_quality(self, data: Dict[str, Any]) -> float:
        """Assess the quality of collected data."""
        if not data:
            return 0.0
        
        quality_score = 1.0
        
        # Check for errors
        error_count = sum(1 for item in data.values() if isinstance(item, dict) and "error" in item)
        if error_count > 0:
            quality_score -= (error_count / len(data)) * 0.5
        
        # Check data completeness
        empty_count = sum(1 for item in data.values() if not item or (isinstance(item, dict) and not item))
        if empty_count > 0:
            quality_score -= (empty_count / len(data)) * 0.3
        
        # Ensure score is between 0 and 1
        return max(0.0, min(1.0, quality_score))
    
    async def start_periodic_crawling(self):
        """Start periodic data crawling in the background."""
        logger.info(f"Starting periodic crawling every {self.crawl_interval_minutes} minutes")
        
        while True:
            try:
                # Crawl market data for major indices
                await self._periodic_market_crawl()
                
                # Crawl news data
                await self._periodic_news_crawl()
                
                # Wait for next interval
                await asyncio.sleep(self.crawl_interval_minutes * 60)
                
            except Exception as e:
                logger.error(f"Error in periodic crawling: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    async def _periodic_market_crawl(self):
        """Periodic market data crawling."""
        major_symbols = ["SPY", "QQQ", "IWM", "VIX", "DXY"]
        
        request = DataMessage(
            message_type=MessageType.DATA_REQUEST,
            sender_id=self.agent_id,
            data_type="market",
            symbols=major_symbols,
        )
        
        await self.process_message(request, None)
    
    async def _periodic_news_crawl(self):
        """Periodic news data crawling."""
        request = DataMessage(
            message_type=MessageType.DATA_REQUEST,
            sender_id=self.agent_id,
            data_type="news",
        )
        
        await self.process_message(request, None)
