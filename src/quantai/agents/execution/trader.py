"""
Execution Agent (Trader) for the QuantAI multi-agent system.

This agent is responsible for executing trades based on strategy signals
and managing order execution in the live trading environment.
"""

import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional

from autogen_core import Agent, MessageContext
from ...core.base import BaseQuantAgent, AgentRole, AgentCapability
from ...core.messages import QuantMessage, MessageType, TradeMessage


class ExecutionAgent(BaseQuantAgent):
    """
    Agent responsible for executing trades in the live market.
    
    This agent receives trade signals from strategies and executes
    them in the live trading environment, managing order flow
    and execution quality.
    """
    
    def __init__(self, agent_id: str = "execution"):
        super().__init__(
            role=AgentRole.EXECUTION,
            capabilities=[
                AgentCapability.TRADE_EXECUTION,
                AgentCapability.ORDER_MANAGEMENT,
                AgentCapability.MARKET_ACCESS,
            ]
        )
        self.agent_id = agent_id
        self.pending_orders: Dict[str, Dict[str, Any]] = {}
        self.executed_trades: List[Dict[str, Any]] = []
        self.execution_stats = {
            'total_trades': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_volume': 0.0
        }
    
    async def on_messages(self, messages: List[QuantMessage], ctx: MessageContext) -> str:
        """Handle incoming messages for trade execution."""
        results = []
        
        for message in messages:
            if isinstance(message, TradeMessage):
                result = await self._handle_trade_execution(message)
                results.append(result)
            else:
                result = await self._handle_general_message(message)
                results.append(result)
        
        return f"ExecutionAgent processed {len(results)} messages"
    
    async def _handle_trade_execution(self, message: TradeMessage) -> Dict[str, Any]:
        """Handle trade execution request."""
        try:
            trade_id = message.trade_id
            
            # Validate trade request
            if not await self._validate_trade_request(message):
                return {
                    'status': 'rejected',
                    'trade_id': trade_id,
                    'error': 'Trade validation failed'
                }
            
            # Execute trade
            execution_result = await self._execute_trade(message)
            
            # Record execution
            self.executed_trades.append({
                'trade_id': trade_id,
                'execution_time': datetime.now(),
                'status': execution_result['status'],
                'details': execution_result
            })
            
            # Update statistics
            self._update_execution_stats(execution_result)
            
            return execution_result
            
        except Exception as e:
            return {
                'status': 'error',
                'trade_id': getattr(message, 'trade_id', 'unknown'),
                'error': str(e)
            }
    
    async def _validate_trade_request(self, trade: TradeMessage) -> bool:
        """Validate trade request before execution."""
        # Check required fields
        if not trade.symbol or not trade.action or not trade.quantity:
            return False
        
        # Check action is valid
        if trade.action not in ['BUY', 'SELL']:
            return False
        
        # Check quantity is positive
        if trade.quantity <= 0:
            return False
        
        # Additional validation logic would go here
        # - Risk checks
        # - Position limits
        # - Market hours
        # - Symbol validity
        
        return True
    
    async def _execute_trade(self, trade: TradeMessage) -> Dict[str, Any]:
        """Execute trade in the market."""
        trade_id = trade.trade_id
        
        # Add to pending orders
        self.pending_orders[trade_id] = {
            'symbol': trade.symbol,
            'action': trade.action,
            'quantity': trade.quantity,
            'order_type': trade.order_type,
            'submit_time': datetime.now(),
            'status': 'pending'
        }
        
        # Simulate trade execution
        await asyncio.sleep(0.1)  # Simulate execution time
        
        # Simulate execution result (in real system, this would be actual market execution)
        import random
        
        # Simulate fill price (small random variation from requested price)
        base_price = trade.price if trade.price else 100.0
        fill_price = base_price * (1 + random.uniform(-0.001, 0.001))  # 0.1% variation
        
        # Simulate partial or full fill
        fill_quantity = trade.quantity * random.uniform(0.95, 1.0)  # 95-100% fill
        
        # Update order status
        self.pending_orders[trade_id]['status'] = 'filled'
        self.pending_orders[trade_id]['fill_price'] = fill_price
        self.pending_orders[trade_id]['fill_quantity'] = fill_quantity
        self.pending_orders[trade_id]['fill_time'] = datetime.now()
        
        return {
            'status': 'filled',
            'trade_id': trade_id,
            'symbol': trade.symbol,
            'action': trade.action,
            'requested_quantity': trade.quantity,
            'fill_quantity': fill_quantity,
            'fill_price': fill_price,
            'execution_time': datetime.now(),
            'message': f'Trade {trade_id} executed successfully'
        }
    
    async def _handle_general_message(self, message: QuantMessage) -> Dict[str, Any]:
        """Handle general messages."""
        return {
            'status': 'processed',
            'message_type': message.message_type.value,
            'sender': message.sender_id
        }

    async def process_message(self, message: QuantMessage, ctx: MessageContext) -> Optional[QuantMessage]:
        """Process a single message (required by BaseQuantAgent)."""
        try:
            if isinstance(message, TradeMessage):
                result = await self._handle_trade_execution(message)
                return QuantMessage(
                    message_type=MessageType.TRADE_RESPONSE,
                    sender_id=self.agent_id,
                    data_payload=result
                )
            else:
                result = await self._handle_general_message(message)
                return QuantMessage(
                    message_type=MessageType.GENERAL_RESPONSE,
                    sender_id=self.agent_id,
                    data_payload=result
                )
        except Exception as e:
            return QuantMessage(
                message_type=MessageType.ERROR,
                sender_id=self.agent_id,
                error_message=str(e)
            )
    
    def _update_execution_stats(self, execution_result: Dict[str, Any]) -> None:
        """Update execution statistics."""
        self.execution_stats['total_trades'] += 1
        
        if execution_result['status'] == 'filled':
            self.execution_stats['successful_trades'] += 1
            self.execution_stats['total_volume'] += execution_result.get('fill_quantity', 0)
        else:
            self.execution_stats['failed_trades'] += 1
    
    def get_pending_orders(self) -> Dict[str, Dict[str, Any]]:
        """Get list of pending orders."""
        return {k: v for k, v in self.pending_orders.items() if v['status'] == 'pending'}
    
    def get_execution_history(self) -> List[Dict[str, Any]]:
        """Get execution history."""
        return self.executed_trades.copy()
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """Get execution statistics."""
        return self.execution_stats.copy()
