"""
Model client factory for creating and managing AI model clients.

This module provides a factory pattern for creating model clients
with automatic configuration and fallback capabilities.
"""

from typing import Dict, List, Optional, Type, Union

from autogen_core.models import ChatCompletionClient
from autogen_ext.models.openai import OpenAIChatCompletionClient
from loguru import logger

from ..core.config import ModelConfig


class ModelClientFactory:
    """Factory for creating model clients with automatic fallback."""
    
    _client_classes: Dict[str, Type[ChatCompletionClient]] = {}
    _fallback_order = ["openai", "anthropic", "google"]
    
    @classmethod
    def register_client(cls, provider: str, client_class: Type[ChatCompletionClient]):
        """Register a model client class for a provider."""
        cls._client_classes[provider] = client_class
        logger.info(f"Registered model client for provider: {provider}")
    
    @classmethod
    def create_client(
        cls, 
        config: ModelConfig,
        fallback_configs: Optional[List[ModelConfig]] = None
    ) -> ChatCompletionClient:
        """
        Create a model client based on configuration.
        
        Args:
            config: Primary model configuration
            fallback_configs: Optional fallback configurations
            
        Returns:
            ChatCompletionClient instance
        """
        try:
            # Try to create primary client
            client = cls._create_single_client(config)
            
            # If fallback configs provided, wrap in MultiModelClient
            if fallback_configs:
                fallback_clients = []
                for fallback_config in fallback_configs:
                    try:
                        fallback_client = cls._create_single_client(fallback_config)
                        fallback_clients.append(fallback_client)
                    except Exception as e:
                        logger.warning(f"Failed to create fallback client: {e}")
                
                if fallback_clients:
                    from .clients import MultiModelClient
                    return MultiModelClient(
                        primary_client=client,
                        fallback_clients=fallback_clients
                    )
            
            return client
            
        except Exception as e:
            logger.error(f"Failed to create model client: {e}")
            raise
    
    @classmethod
    def _create_single_client(cls, config: ModelConfig) -> ChatCompletionClient:
        """Create a single model client from configuration."""
        provider = config.provider.lower()
        
        if provider == "openai":
            return cls._create_openai_client(config)
        elif provider == "anthropic":
            return cls._create_anthropic_client(config)
        elif provider == "google":
            return cls._create_google_client(config)
        else:
            raise ValueError(f"Unsupported model provider: {provider}")
    
    @classmethod
    def _create_openai_client(cls, config: ModelConfig) -> ChatCompletionClient:
        """Create OpenAI client."""
        kwargs = {
            "model": config.model_name,
            "api_key": config.api_key,
            "timeout": config.timeout,
            "max_retries": config.max_retries,
        }
        
        if config.api_base:
            kwargs["base_url"] = config.api_base
        if config.temperature is not None:
            kwargs["temperature"] = config.temperature
        if config.max_tokens:
            kwargs["max_tokens"] = config.max_tokens
        
        return OpenAIChatCompletionClient(**kwargs)
    
    @classmethod
    def _create_anthropic_client(cls, config: ModelConfig) -> ChatCompletionClient:
        """Create Anthropic client."""
        try:
            from autogen_ext.models.anthropic import AnthropicChatCompletionClient
            
            kwargs = {
                "model": config.model_name,
                "api_key": config.api_key,
                "timeout": config.timeout,
                "max_retries": config.max_retries,
            }
            
            if config.temperature is not None:
                kwargs["temperature"] = config.temperature
            if config.max_tokens:
                kwargs["max_tokens"] = config.max_tokens
            
            return AnthropicChatCompletionClient(**kwargs)
            
        except ImportError:
            logger.error("Anthropic client not available. Install autogen-ext[anthropic]")
            raise
    
    @classmethod
    def _create_google_client(cls, config: ModelConfig) -> ChatCompletionClient:
        """Create Google client."""
        try:
            from autogen_ext.models.google import GoogleChatCompletionClient
            
            kwargs = {
                "model": config.model_name,
                "api_key": config.api_key,
                "timeout": config.timeout,
                "max_retries": config.max_retries,
            }
            
            if config.temperature is not None:
                kwargs["temperature"] = config.temperature
            if config.max_tokens:
                kwargs["max_tokens"] = config.max_tokens
            
            return GoogleChatCompletionClient(**kwargs)
            
        except ImportError:
            logger.error("Google client not available. Install autogen-ext[google]")
            raise
    
    @classmethod
    def create_with_fallback(
        cls,
        primary_config: ModelConfig,
        fallback_providers: Optional[List[str]] = None
    ) -> ChatCompletionClient:
        """
        Create a client with automatic fallback to other providers.
        
        Args:
            primary_config: Primary model configuration
            fallback_providers: List of fallback providers to try
            
        Returns:
            ChatCompletionClient with fallback capabilities
        """
        fallback_providers = fallback_providers or cls._fallback_order
        
        # Create fallback configs
        fallback_configs = []
        for provider in fallback_providers:
            if provider != primary_config.provider:
                fallback_config = ModelConfig(
                    provider=provider,
                    model_name=cls._get_default_model(provider),
                    api_key=cls._get_api_key(provider),
                    temperature=primary_config.temperature,
                    max_tokens=primary_config.max_tokens,
                    timeout=primary_config.timeout,
                    max_retries=primary_config.max_retries,
                )
                fallback_configs.append(fallback_config)
        
        return cls.create_client(primary_config, fallback_configs)
    
    @classmethod
    def _get_default_model(cls, provider: str) -> str:
        """Get default model for a provider."""
        defaults = {
            "openai": "gpt-4o",
            "anthropic": "claude-3-sonnet-20240229",
            "google": "gemini-pro",
        }
        return defaults.get(provider, "gpt-4o")
    
    @classmethod
    def _get_api_key(cls, provider: str) -> Optional[str]:
        """Get API key for a provider from environment."""
        import os
        
        key_mapping = {
            "openai": "OPENAI_API_KEY",
            "anthropic": "ANTHROPIC_API_KEY", 
            "google": "GOOGLE_API_KEY",
        }
        
        env_var = key_mapping.get(provider)
        return os.getenv(env_var) if env_var else None


def create_model_client(
    config: ModelConfig,
    fallback_configs: Optional[List[ModelConfig]] = None,
    enable_fallback: bool = True
) -> ChatCompletionClient:
    """
    Convenience function to create a model client.
    
    Args:
        config: Model configuration
        fallback_configs: Optional fallback configurations
        enable_fallback: Whether to enable automatic fallback
        
    Returns:
        ChatCompletionClient instance
    """
    factory = ModelClientFactory()
    
    if enable_fallback and not fallback_configs:
        return factory.create_with_fallback(config)
    else:
        return factory.create_client(config, fallback_configs)


def create_multi_model_client(
    configs: List[ModelConfig],
    strategy: str = "round_robin"
) -> ChatCompletionClient:
    """
    Create a multi-model client that can use multiple models.
    
    Args:
        configs: List of model configurations
        strategy: Strategy for model selection ("round_robin", "random", "performance")
        
    Returns:
        MultiModelClient instance
    """
    from .clients import MultiModelClient
    
    factory = ModelClientFactory()
    clients = []
    
    for config in configs:
        try:
            client = factory._create_single_client(config)
            clients.append(client)
        except Exception as e:
            logger.warning(f"Failed to create client for {config.provider}: {e}")
    
    if not clients:
        raise ValueError("No valid model clients could be created")
    
    return MultiModelClient(
        primary_client=clients[0],
        fallback_clients=clients[1:],
        selection_strategy=strategy
    )
