"""
Custom model clients with enhanced capabilities for the QuantAI system.

This module provides wrapper clients that add features like automatic
fallback, load balancing, and performance tracking.
"""

import asyncio
import random
import time
from typing import Any, Dict, List, Optional, Sequence, Union

from autogen_core.models import (
    ChatCompletionClient,
    CreateResult,
    LLMMessage,
    RequestUsage,
)
from loguru import logger


class MultiModelClient(ChatCompletionClient):
    """
    A client that can use multiple model providers with automatic fallback.
    
    Supports different strategies for model selection:
    - round_robin: Cycle through models
    - random: Random selection
    - performance: Select based on performance metrics
    """
    
    def __init__(
        self,
        primary_client: ChatCompletionClient,
        fallback_clients: List[ChatCompletionClient],
        selection_strategy: str = "round_robin",
        max_retries_per_client: int = 2,
        fallback_delay: float = 1.0,
    ):
        self.primary_client = primary_client
        self.fallback_clients = fallback_clients
        self.all_clients = [primary_client] + fallback_clients
        self.selection_strategy = selection_strategy
        self.max_retries_per_client = max_retries_per_client
        self.fallback_delay = fallback_delay
        
        # Performance tracking
        self._client_stats: Dict[str, Dict[str, Any]] = {}
        self._current_index = 0
        
        # Initialize stats for each client
        for i, client in enumerate(self.all_clients):
            client_id = f"client_{i}"
            self._client_stats[client_id] = {
                "requests": 0,
                "successes": 0,
                "failures": 0,
                "avg_response_time": 0.0,
                "last_used": 0,
                "client": client,
            }
    
    async def create(
        self,
        messages: Sequence[LLMMessage],
        tools: Sequence[Any] = [],
        json_output: Optional[bool] = None,
        extra_create_args: Dict[str, Any] = {},
        cancellation_token: Optional[Any] = None,
    ) -> CreateResult:
        """Create a chat completion using the configured strategy."""
        
        # Select client based on strategy
        selected_clients = self._select_clients()
        
        last_exception = None
        
        for client_id, client in selected_clients:
            for attempt in range(self.max_retries_per_client):
                try:
                    start_time = time.time()
                    
                    # Track request
                    self._client_stats[client_id]["requests"] += 1
                    self._client_stats[client_id]["last_used"] = time.time()
                    
                    # Make the request
                    result = await client.create(
                        messages=messages,
                        tools=tools,
                        json_output=json_output,
                        extra_create_args=extra_create_args,
                        cancellation_token=cancellation_token,
                    )
                    
                    # Track success
                    response_time = time.time() - start_time
                    self._update_stats(client_id, True, response_time)
                    
                    logger.debug(f"Successfully used {client_id} in {response_time:.2f}s")
                    return result
                    
                except Exception as e:
                    last_exception = e
                    response_time = time.time() - start_time
                    self._update_stats(client_id, False, response_time)
                    
                    logger.warning(
                        f"Attempt {attempt + 1} failed for {client_id}: {e}"
                    )
                    
                    if attempt < self.max_retries_per_client - 1:
                        await asyncio.sleep(self.fallback_delay)
            
            # If we have more clients to try, add a delay before fallback
            if client != selected_clients[-1][1]:
                await asyncio.sleep(self.fallback_delay)
        
        # All clients failed
        logger.error("All model clients failed")
        raise last_exception or Exception("All model clients failed")
    
    def _select_clients(self) -> List[tuple[str, ChatCompletionClient]]:
        """Select clients based on the configured strategy."""
        if self.selection_strategy == "round_robin":
            return self._round_robin_selection()
        elif self.selection_strategy == "random":
            return self._random_selection()
        elif self.selection_strategy == "performance":
            return self._performance_selection()
        else:
            # Default to round robin
            return self._round_robin_selection()
    
    def _round_robin_selection(self) -> List[tuple[str, ChatCompletionClient]]:
        """Round robin client selection."""
        clients = []
        
        # Start with the next client in rotation
        for i in range(len(self.all_clients)):
            index = (self._current_index + i) % len(self.all_clients)
            client_id = f"client_{index}"
            clients.append((client_id, self.all_clients[index]))
        
        self._current_index = (self._current_index + 1) % len(self.all_clients)
        return clients
    
    def _random_selection(self) -> List[tuple[str, ChatCompletionClient]]:
        """Random client selection."""
        indices = list(range(len(self.all_clients)))
        random.shuffle(indices)
        
        return [
            (f"client_{i}", self.all_clients[i])
            for i in indices
        ]
    
    def _performance_selection(self) -> List[tuple[str, ChatCompletionClient]]:
        """Performance-based client selection."""
        # Sort clients by success rate and response time
        client_scores = []
        
        for client_id, stats in self._client_stats.items():
            if stats["requests"] > 0:
                success_rate = stats["successes"] / stats["requests"]
                avg_time = stats["avg_response_time"]
                # Score combines success rate and speed (lower time is better)
                score = success_rate - (avg_time / 10.0)  # Normalize time impact
            else:
                score = 0.5  # Neutral score for unused clients
            
            client_scores.append((client_id, score, stats["client"]))
        
        # Sort by score (descending)
        client_scores.sort(key=lambda x: x[1], reverse=True)
        
        return [(client_id, client) for client_id, _, client in client_scores]
    
    def _update_stats(self, client_id: str, success: bool, response_time: float):
        """Update performance statistics for a client."""
        stats = self._client_stats[client_id]
        
        if success:
            stats["successes"] += 1
        else:
            stats["failures"] += 1
        
        # Update average response time
        total_requests = stats["successes"] + stats["failures"]
        if total_requests > 1:
            stats["avg_response_time"] = (
                (stats["avg_response_time"] * (total_requests - 1) + response_time) /
                total_requests
            )
        else:
            stats["avg_response_time"] = response_time
    
    def get_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get performance statistics for all clients."""
        stats = {}
        for client_id, client_stats in self._client_stats.items():
            stats[client_id] = {
                "requests": client_stats["requests"],
                "successes": client_stats["successes"],
                "failures": client_stats["failures"],
                "success_rate": (
                    client_stats["successes"] / max(client_stats["requests"], 1)
                ),
                "avg_response_time": client_stats["avg_response_time"],
                "last_used": client_stats["last_used"],
            }
        return stats
    
    async def close(self):
        """Close all underlying clients."""
        for client in self.all_clients:
            try:
                await client.close()
            except Exception as e:
                logger.warning(f"Error closing client: {e}")


class RateLimitedClient(ChatCompletionClient):
    """
    A wrapper client that implements rate limiting.
    """
    
    def __init__(
        self,
        client: ChatCompletionClient,
        requests_per_minute: int = 60,
        requests_per_hour: int = 3600,
    ):
        self.client = client
        self.requests_per_minute = requests_per_minute
        self.requests_per_hour = requests_per_hour
        
        # Track request timestamps
        self._minute_requests: List[float] = []
        self._hour_requests: List[float] = []
        self._lock = asyncio.Lock()
    
    async def create(
        self,
        messages: Sequence[LLMMessage],
        tools: Sequence[Any] = [],
        json_output: Optional[bool] = None,
        extra_create_args: Dict[str, Any] = {},
        cancellation_token: Optional[Any] = None,
    ) -> CreateResult:
        """Create a chat completion with rate limiting."""
        
        async with self._lock:
            await self._wait_for_rate_limit()
            
            # Record request time
            now = time.time()
            self._minute_requests.append(now)
            self._hour_requests.append(now)
            
            # Clean old requests
            self._clean_old_requests(now)
        
        # Make the actual request
        return await self.client.create(
            messages=messages,
            tools=tools,
            json_output=json_output,
            extra_create_args=extra_create_args,
            cancellation_token=cancellation_token,
        )
    
    async def _wait_for_rate_limit(self):
        """Wait if rate limits would be exceeded."""
        now = time.time()
        
        # Check minute limit
        minute_ago = now - 60
        recent_minute_requests = [
            t for t in self._minute_requests if t > minute_ago
        ]
        
        if len(recent_minute_requests) >= self.requests_per_minute:
            wait_time = 60 - (now - recent_minute_requests[0])
            if wait_time > 0:
                logger.info(f"Rate limit: waiting {wait_time:.1f}s for minute limit")
                await asyncio.sleep(wait_time)
        
        # Check hour limit
        hour_ago = now - 3600
        recent_hour_requests = [
            t for t in self._hour_requests if t > hour_ago
        ]
        
        if len(recent_hour_requests) >= self.requests_per_hour:
            wait_time = 3600 - (now - recent_hour_requests[0])
            if wait_time > 0:
                logger.info(f"Rate limit: waiting {wait_time:.1f}s for hour limit")
                await asyncio.sleep(wait_time)
    
    def _clean_old_requests(self, now: float):
        """Remove old request timestamps."""
        minute_ago = now - 60
        hour_ago = now - 3600
        
        self._minute_requests = [t for t in self._minute_requests if t > minute_ago]
        self._hour_requests = [t for t in self._hour_requests if t > hour_ago]
    
    async def close(self):
        """Close the underlying client."""
        await self.client.close()


class CachingClient(ChatCompletionClient):
    """
    A wrapper client that implements response caching.
    """
    
    def __init__(
        self,
        client: ChatCompletionClient,
        cache_size: int = 1000,
        cache_ttl: int = 3600,  # 1 hour
    ):
        self.client = client
        self.cache_size = cache_size
        self.cache_ttl = cache_ttl
        
        # Simple in-memory cache
        self._cache: Dict[str, tuple[CreateResult, float]] = {}
    
    async def create(
        self,
        messages: Sequence[LLMMessage],
        tools: Sequence[Any] = [],
        json_output: Optional[bool] = None,
        extra_create_args: Dict[str, Any] = {},
        cancellation_token: Optional[Any] = None,
    ) -> CreateResult:
        """Create a chat completion with caching."""
        
        # Generate cache key
        cache_key = self._generate_cache_key(
            messages, tools, json_output, extra_create_args
        )
        
        # Check cache
        now = time.time()
        if cache_key in self._cache:
            result, timestamp = self._cache[cache_key]
            if now - timestamp < self.cache_ttl:
                logger.debug("Cache hit for request")
                return result
            else:
                # Remove expired entry
                del self._cache[cache_key]
        
        # Make request
        result = await self.client.create(
            messages=messages,
            tools=tools,
            json_output=json_output,
            extra_create_args=extra_create_args,
            cancellation_token=cancellation_token,
        )
        
        # Cache result
        self._cache[cache_key] = (result, now)
        
        # Manage cache size
        if len(self._cache) > self.cache_size:
            # Remove oldest entries
            sorted_items = sorted(
                self._cache.items(),
                key=lambda x: x[1][1]  # Sort by timestamp
            )
            for key, _ in sorted_items[:len(self._cache) - self.cache_size]:
                del self._cache[key]
        
        return result
    
    def _generate_cache_key(
        self,
        messages: Sequence[LLMMessage],
        tools: Sequence[Any],
        json_output: Optional[bool],
        extra_create_args: Dict[str, Any],
    ) -> str:
        """Generate a cache key for the request."""
        import hashlib
        import json
        
        # Create a deterministic representation
        key_data = {
            "messages": [
                {"role": getattr(msg, "role", ""), "content": str(msg.content)}
                for msg in messages
            ],
            "tools": str(tools),
            "json_output": json_output,
            "extra_args": extra_create_args,
        }
        
        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    async def close(self):
        """Close the underlying client."""
        await self.client.close()


# Convenience aliases
OpenAIClient = None  # Will be set by factory
AnthropicClient = None  # Will be set by factory  
GoogleClient = None  # Will be set by factory
